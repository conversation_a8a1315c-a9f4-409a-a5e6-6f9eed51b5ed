#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
URDF整合查看器 - 主程序入口

这是模块化重构后的主程序文件，使用新的模块结构。

使用方法:
    python main.py

功能特性:
- 3D URDF模型可视化
- 智能关节分组控制
- 实时运动学计算
- 材质和视觉效果控制
- 预设动作和随机姿态
"""

import sys
import os

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# 导入主窗口类
from src import URDFIntegratedViewer

def main():
    """主函数 - 启动URDF整合查看器"""
    try:
        # 导入PySide6应用程序类
        from PySide6.QtWidgets import QApplication
        
        # 创建应用程序实例
        app = QApplication(sys.argv)
        
        # 设置应用程序属性
        app.setApplicationName("URDF整合查看器")
        app.setApplicationVersion("2.0")
        app.setOrganizationName("URDF Tools")
        
        # 创建并显示主窗口
        viewer = URDFIntegratedViewer()
        viewer.show()
        
        print("URDF整合查看器已启动")
        print("使用模块化架构 v2.0")
        print("=" * 50)
        
        # 运行应用程序主循环
        return app.exec()
        
    except ImportError as e:
        print(f"导入错误: {e}")
        print("请确保已安装所需的依赖包:")
        print("- PySide6")
        print("- pyqtgraph")
        print("- numpy")
        print("- trimesh")
        print("- numpy-stl")
        print("- urdfpy")
        return 1
        
    except Exception as e:
        print(f"启动应用程序时出错: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
